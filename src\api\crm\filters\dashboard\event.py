"""
Event Dashboard Filters for CRM
Provides filtering capabilities for event dashboard analytics
"""

from django_filters import rest_framework as filters
from core.models import EventSchedule, Event, Offering
from django.db.models import Q


class CrmDashboardEventFilter(filters.FilterSet):
    """
    Filter for Event Dashboard analytics
    Supports filtering by date ranges, programs (offerings), and specific events
    """

    # Date range filters
    created_at_after = filters.DateFilter(
        method="filter_by_date_range",
        help_text="Start date for filtering (YYYY-MM-DD format). Used with created_at_before and date_field.",
    )
    created_at_before = filters.DateFilter(
        method="filter_by_date_range", 
        help_text="End date for filtering (YYYY-MM-DD format). Used with created_at_after and date_field.",
    )
    
    # Date field selector - determines which date field to filter by
    date_field = filters.ChoiceFilter(
        method="filter_by_date_range",
        choices=[
            ("created_at", "Fecha de Creación"),
            ("start_date", "Fecha de Programación"),
        ],
        help_text="Field to apply date range filter to. Options: created_at (creation date), start_date (scheduled date)",
    )

    # Program filter (offerings) - multiple selection
    programs = filters.CharFilter(
        method="filter_by_programs",
        help_text="Filter by programs/offerings (comma-separated list of offering IDs)",
    )

    # Specific event filter - single selection
    event = filters.UUIDFilter(
        field_name="event__eid",
        help_text="Filter by specific event ID",
    )

    # Event stage filter
    stage = filters.CharFilter(
        method="filter_by_stage",
        help_text="Filter by event stage (comma-separated list)",
    )

    # Event type filter
    event_type = filters.CharFilter(
        method="filter_by_event_type",
        help_text="Filter by event type (comma-separated list)",
    )

    # Event modality filter
    modality = filters.CharFilter(
        method="filter_by_modality",
        help_text="Filter by event modality (comma-separated list)",
    )

    class Meta:
        model = EventSchedule
        fields = [
            "created_at_after",
            "created_at_before", 
            "date_field",
            "programs",
            "event",
            "stage",
            "event_type",
            "modality",
        ]

    def filter_by_date_range(self, queryset, name, value):
        """
        Filter by date range based on the selected date field
        Requires created_at_after, created_at_before, and date_field parameters
        """
        params = self.request.GET
        start_date = params.get("created_at_after")
        end_date = params.get("created_at_before")
        date_field = params.get("date_field", "created_at")

        # All parameters are required for date filtering
        if not all([start_date, end_date, date_field]):
            return queryset

        if date_field == "created_at":
            return queryset.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date
            )
        elif date_field == "start_date":
            return queryset.filter(
                start_date__date__gte=start_date,
                start_date__date__lte=end_date
            )

        return queryset

    def filter_by_programs(self, queryset, name, value):
        """
        Filter by programs/offerings (comma-separated list of offering IDs)
        Example: ?programs=uuid1,uuid2,uuid3
        """
        if not value:
            return queryset

        program_ids = [pid.strip() for pid in value.split(",")]
        return queryset.filter(event__offering__oid__in=program_ids).distinct()

    def filter_by_stage(self, queryset, name, value):
        """
        Filter by event stage (comma-separated list)
        Example: ?stage=planning,launched
        """
        if not value:
            return queryset

        stages = [stage.strip() for stage in value.split(",")]
        return queryset.filter(stage__in=stages)

    def filter_by_event_type(self, queryset, name, value):
        """
        Filter by event type (comma-separated list)
        Example: ?event_type=workshop,webinar
        """
        if not value:
            return queryset

        types = [event_type.strip() for event_type in value.split(",")]
        return queryset.filter(event__type__in=types)

    def filter_by_modality(self, queryset, name, value):
        """
        Filter by event modality (comma-separated list)
        Example: ?modality=remote,in_person
        """
        if not value:
            return queryset

        modalities = [modality.strip() for modality in value.split(",")]
        return queryset.filter(modality__in=modalities)
