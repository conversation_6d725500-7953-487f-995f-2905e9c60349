"""
Event Dashboard Views for CRM
Provides analytics endpoints for event dashboard
"""

from django.db.models import Q, Sum, Case, When, IntegerField, F, Count, Value, Avg
from django.db.models.functions import Coalesce
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from decimal import Decimal
from core.models import (
    EventSchedule,
    EventScheduleEnrollment,
    Event,
    Offering,
    Order,
    OrderItem,
    User,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.event import CrmDashboardEventFilter
from api.crm.serializers.dashboard.event import (
    CrmDashboardEventSerializer,
    CrmDashboardEventFilterOptionsSerializer,
    CrmDashboardEventStatsSerializer,
    CrmDashboardEventTypeChartSerializer,
    CrmDashboardEventDiffusionChannelChartSerializer,
    CrmDashboardEventInterestsChartSerializer,
    CrmDashboardEventSegmentationChartSerializer,
    CrmDashboardEventDynamicMetricsSerializer,
)
from api.crm.utils.dashboard import DashboardUtils
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.conf import settings
import json


class CrmDashboardEventViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Event Dashboard Analytics
    Provides various endpoints for event dashboard statistics and charts
    """

    model_class = EventSchedule
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    filterset_class = CrmDashboardEventFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardEventSerializer

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_event")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for event schedules (non-deleted schedules only)
        """
        return EventSchedule.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "created_at_after": self.request.GET.get("created_at_after", ""),
            "created_at_before": self.request.GET.get("created_at_before", ""),
            "date_field": self.request.GET.get("date_field", ""),
            "programs": self.request.GET.get("programs", ""),
            "event": self.request.GET.get("event", ""),
            "stage": self.request.GET.get("stage", ""),
            "event_type": self.request.GET.get("event_type", ""),
            "modality": self.request.GET.get("modality", ""),
        }

    def _get_queryset_excluding_filters(self, exclude_fields):
        """
        Get queryset excluding specific filters for comparison calculations
        """
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    def _get_report_dates(self):
        """
        Get current and previous period dates for trend calculations
        """
        return DashboardUtils.get_report_dates(self.request)

    @action(detail=False, methods=["get"])
    def stats(self, request):
        """
        Get general event statistics with trend analysis
        """
        cache_key = f"event_stats_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered queryset
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Get report dates for trend analysis
        dates = self._get_report_dates()

        # Current period enrollments
        current_enrollments = enrollments.filter(
            created_at__gte=dates["current_start"], created_at__lte=dates["current_end"]
        )

        # Previous period enrollments for comparison
        previous_enrollments = enrollments.filter(
            created_at__gte=dates["previous_start"],
            created_at__lte=dates["previous_end"],
        )

        # Calculate basic stats
        total_enrollments = current_enrollments.count()
        total_enrollments_prev = previous_enrollments.count()

        with_contact = current_enrollments.filter(has_contact=True).count()
        with_contact_prev = previous_enrollments.filter(has_contact=True).count()

        without_contact = current_enrollments.filter(has_contact=False).count()

        already_leads = current_enrollments.filter(already_lead=True).count()
        already_leads_prev = previous_enrollments.filter(already_lead=True).count()

        need_conciliation = current_enrollments.filter(needs_conciliation=True).count()

        with_partnership = current_enrollments.filter(partnership__isnull=False).count()

        # Calculate conversion metrics (enrollments that resulted in sales)
        enrollment_emails = list(current_enrollments.values_list("email", flat=True))
        enrollment_to_sales = Order.objects.filter(
            owner__email__in=enrollment_emails, stage=Order.SOLD_STAGE, deleted=False
        ).count()

        prev_enrollment_emails = list(
            previous_enrollments.values_list("email", flat=True)
        )
        enrollment_to_sales_prev = Order.objects.filter(
            owner__email__in=prev_enrollment_emails,
            stage=Order.SOLD_STAGE,
            deleted=False,
        ).count()

        # Program match sales (sales that match the event's offering)
        program_match_sales = 0
        if enrollment_emails:
            # Get unique offerings from current event schedules
            event_offerings = event_schedules.values_list(
                "event__offering__oid", flat=True
            ).distinct()

            program_match_sales = (
                Order.objects.filter(
                    owner__email__in=enrollment_emails,
                    stage=Order.SOLD_STAGE,
                    deleted=False,
                )
                .filter(items__offering__oid__in=event_offerings)
                .distinct()
                .count()
            )

        # Calculate percentages and trends
        with_contact_percentage = (
            (with_contact / total_enrollments * 100) if total_enrollments > 0 else 0
        )
        without_contact_percentage = (
            (without_contact / total_enrollments * 100) if total_enrollments > 0 else 0
        )
        already_leads_percentage = (
            (already_leads / total_enrollments * 100) if total_enrollments > 0 else 0
        )
        need_conciliation_percentage = (
            (need_conciliation / total_enrollments * 100)
            if total_enrollments > 0
            else 0
        )
        with_partnership_percentage = (
            (with_partnership / total_enrollments * 100) if total_enrollments > 0 else 0
        )
        enrollment_to_sales_percentage = (
            (enrollment_to_sales / total_enrollments * 100)
            if total_enrollments > 0
            else 0
        )
        program_match_percentage = (
            (program_match_sales / enrollment_to_sales * 100)
            if enrollment_to_sales > 0
            else 0
        )

        # Calculate trends
        total_change, total_tendency = DashboardUtils.calculate_percentage_change(
            total_enrollments, total_enrollments_prev
        )
        contact_change, contact_tendency = DashboardUtils.calculate_percentage_change(
            with_contact, with_contact_prev
        )
        leads_change, leads_tendency = DashboardUtils.calculate_percentage_change(
            already_leads, already_leads_prev
        )
        sales_change, sales_tendency = DashboardUtils.calculate_percentage_change(
            enrollment_to_sales, enrollment_to_sales_prev
        )

        stats_data = {
            "total_enrollments": total_enrollments,
            "total_enrollments_change": total_change,
            "total_enrollments_tendency": total_tendency,
            "with_contact": with_contact,
            "with_contact_percentage": round(with_contact_percentage, 1),
            "with_contact_change": contact_change,
            "with_contact_tendency": contact_tendency,
            "without_contact": without_contact,
            "without_contact_percentage": round(without_contact_percentage, 1),
            "already_leads": already_leads,
            "already_leads_percentage": round(already_leads_percentage, 1),
            "already_leads_change": leads_change,
            "already_leads_tendency": leads_tendency,
            "need_conciliation": need_conciliation,
            "need_conciliation_percentage": round(need_conciliation_percentage, 1),
            "with_partnership": with_partnership,
            "with_partnership_percentage": round(with_partnership_percentage, 1),
            "enrollment_to_sales": enrollment_to_sales,
            "enrollment_to_sales_percentage": round(enrollment_to_sales_percentage, 1),
            "enrollment_to_sales_change": sales_change,
            "enrollment_to_sales_tendency": sales_tendency,
            "program_match_sales": program_match_sales,
            "program_match_percentage": round(program_match_percentage, 1),
        }

        # Cache the result
        self.cache_manager.set(cache_key, stats_data, timeout=self.cache_timeout)

        return Response(stats_data)

    @action(detail=False, methods=["get"])
    def event_type_chart(self, request):
        """
        Get event type distribution for pie chart
        """
        cache_key = f"event_type_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Count by event type
        type_counts = (
            event_schedules.values("event__type")
            .annotate(count=Count("esid"))
            .order_by("-count")
        )

        total_count = sum(item["count"] for item in type_counts)

        chart_data = []
        for item in type_counts:
            event_type = item["event__type"]
            count = item["count"]
            percentage = (count / total_count * 100) if total_count > 0 else 0

            # Get display name for event type
            type_display = dict(Event.TYPE_CHOICES).get(event_type, event_type)

            chart_data.append(
                {
                    "type": event_type,
                    "type_display": type_display,
                    "count": count,
                    "percentage": round(percentage, 1),
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def diffusion_channel_chart(self, request):
        """
        Get diffusion channel distribution for pie chart
        """
        cache_key = f"diffusion_channel_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Count by diffusion channel
        channel_counts = (
            enrollments.values("diffusion_channel")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        total_count = sum(item["count"] for item in channel_counts)

        chart_data = []
        for item in channel_counts:
            channel = item["diffusion_channel"] or "Sin especificar"
            count = item["count"]
            percentage = (count / total_count * 100) if total_count > 0 else 0

            chart_data.append(
                {"channel": channel, "count": count, "percentage": round(percentage, 1)}
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def interests_chart(self, request):
        """
        Get interests distribution for bar chart (ordered by count)
        """
        cache_key = f"interests_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Count interests from JSON field
        interest_counts = {}
        for enrollment in enrollments:
            if enrollment.interests:
                for interest in enrollment.interests:
                    if isinstance(interest, str):
                        interest_counts[interest] = interest_counts.get(interest, 0) + 1
                    elif isinstance(interest, dict) and "name" in interest:
                        interest_name = interest["name"]
                        interest_counts[interest_name] = (
                            interest_counts.get(interest_name, 0) + 1
                        )

        # Sort by count (descending)
        sorted_interests = sorted(
            interest_counts.items(), key=lambda x: x[1], reverse=True
        )

        total_count = sum(interest_counts.values())

        chart_data = []
        for interest, count in sorted_interests:
            percentage = (count / total_count * 100) if total_count > 0 else 0

            chart_data.append(
                {
                    "interest": interest,
                    "count": count,
                    "percentage": round(percentage, 1),
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def segmentation_chart(self, request):
        """
        Get contact segmentation chart (new vs existing users)
        """
        cache_key = f"segmentation_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Count new vs existing users
        new_users = enrollments.filter(user__isnull=True).count()
        existing_users = enrollments.filter(user__isnull=False).count()

        total_count = new_users + existing_users

        chart_data = []

        if new_users > 0:
            chart_data.append(
                {
                    "segment": "new",
                    "count": new_users,
                    "percentage": (
                        round((new_users / total_count * 100), 1)
                        if total_count > 0
                        else 0
                    ),
                }
            )

        if existing_users > 0:
            chart_data.append(
                {
                    "segment": "existing",
                    "count": existing_users,
                    "percentage": (
                        round((existing_users / total_count * 100), 1)
                        if total_count > 0
                        else 0
                    ),
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def dynamic_metrics(self, request):
        """
        Get dynamic metrics for upcoming/current events
        """
        cache_key = f"dynamic_metrics_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        now = timezone.now()

        # Get upcoming events (next 30 days) and current events
        upcoming_events = EventSchedule.objects.filter(
            deleted=False, start_date__gte=now, start_date__lte=now + timedelta(days=30)
        ).order_by("start_date")

        current_events = EventSchedule.objects.filter(
            deleted=False, start_date__lte=now, end_date__gte=now
        ).order_by("start_date")

        # Combine and get unique events
        all_events = (upcoming_events | current_events).distinct()

        # Apply filters if any
        filterset = self.filterset_class(self.request.GET, queryset=all_events)
        if filterset.is_valid():
            all_events = filterset.qs

        metrics_data = []

        for event_schedule in all_events[:10]:  # Limit to 10 events
            # Calculate days until event
            days_until = (event_schedule.start_date.date() - now.date()).days
            is_current = event_schedule.start_date <= now <= event_schedule.end_date
            is_upcoming = event_schedule.start_date > now

            # Get enrollment count
            total_registered = EventScheduleEnrollment.objects.filter(
                event_schedule=event_schedule, deleted=False
            ).count()

            # Get invitation counts (this would need to be implemented based on your invitation tracking)
            # For now, using placeholder logic
            email_invitations_sent = 0  # TODO: Implement based on your email tracking
            whatsapp_invitations_sent = (
                0  # TODO: Implement based on your WhatsApp tracking
            )

            metrics_data.append(
                {
                    "event_name": event_schedule.event.name,
                    "event_schedule_name": event_schedule.name,
                    "start_date": event_schedule.start_date,
                    "end_date": event_schedule.end_date,
                    "stage": event_schedule.stage,
                    "total_registered": total_registered,
                    "email_invitations_sent": email_invitations_sent,
                    "whatsapp_invitations_sent": whatsapp_invitations_sent,
                    "days_until_event": days_until,
                    "is_current": is_current,
                    "is_upcoming": is_upcoming,
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, metrics_data, timeout=self.cache_timeout)

        return Response(metrics_data)

    @action(detail=False, methods=["get"])
    def filter_options(self, request):
        """
        Get available filter options for the dashboard
        """
        cache_key = "event_filter_options"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get available programs/offerings
        programs = (
            Offering.objects.filter(deleted=False)
            .values("oid", "name", "code_name")
            .order_by("name")
        )

        programs_list = [
            {
                "id": str(program["oid"]),
                "name": program["name"],
                "code_name": program["code_name"],
            }
            for program in programs
        ]

        # Get available events
        events = (
            Event.objects.filter(deleted=False)
            .values("eid", "name", "type")
            .order_by("name")
        )

        events_list = [
            {"id": str(event["eid"]), "name": event["name"], "type": event["type"]}
            for event in events
        ]

        # Get available stages
        stages_list = [
            {"value": choice[0], "label": choice[1]} for choice in Event.STAGE_CHOICES
        ]

        # Get available event types
        event_types_list = [
            {"value": choice[0], "label": choice[1]} for choice in Event.TYPE_CHOICES
        ]

        # Get available modalities
        modalities_list = [
            {"value": choice[0], "label": choice[1]}
            for choice in Event.MODALITY_CHOICES
        ]

        filter_options = {
            "programs": programs_list,
            "events": events_list,
            "stages": stages_list,
            "event_types": event_types_list,
            "modalities": modalities_list,
        }

        # Cache the result for longer (30 minutes)
        self.cache_manager.set(cache_key, filter_options, timeout=60 * 30)

        return Response(filter_options)

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """
        Get complete dashboard data in a single request
        """
        cache_key = f"event_dashboard_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get all dashboard data
        stats_response = self.stats(request)
        event_type_response = self.event_type_chart(request)
        diffusion_response = self.diffusion_channel_chart(request)
        interests_response = self.interests_chart(request)
        segmentation_response = self.segmentation_chart(request)
        dynamic_response = self.dynamic_metrics(request)

        dashboard_data = {
            "stats": stats_response.data,
            "event_type_chart": event_type_response.data,
            "diffusion_channel_chart": diffusion_response.data,
            "interests_chart": interests_response.data,
            "segmentation_chart": segmentation_response.data,
            "dynamic_metrics": dynamic_response.data,
        }

        # Cache the result
        self.cache_manager.set(cache_key, dashboard_data, timeout=self.cache_timeout)

        return Response(dashboard_data)

    @action(detail=False, methods=["post"])
    def invalidate_cache(self, request):
        """
        Invalidate all cached data for event dashboard
        """
        self.cache_manager.invalidate()
        return Response({"message": "Cache invalidated successfully"})
