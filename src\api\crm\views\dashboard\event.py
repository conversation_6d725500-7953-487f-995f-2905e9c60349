"""
Event Dashboard Views for CRM
Provides analytics endpoints for event dashboard
"""

from django.db.models import Q, Case, When, IntegerField, F, Count, Value, Avg
from django.db.models.functions import <PERSON>esce, <PERSON>runc<PERSON>onth, TruncWeek
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from core.models import (
    EventSchedule,
    EventScheduleEnrollment,
    Event,
    Offering,
    Order,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from services.cache.redis import CacheManager
from api.crm.filters.dashboard.event import CrmDashboardEventFilter
from api.crm.serializers.dashboard.event import (
    CrmDashboardEventSerializer,
    CrmDashboardEventStatsSerializer,
    CrmDashboardEventTypeChartSerializer,
    CrmDashboardEventDiffusionChannelChartSerializer,
    CrmDashboardEventInterestsChartSerializer,
    CrmDashboardEventSegmentationChartSerializer,
    CrmDashboardEventDynamicMetricsSerializer,
    CrmDashboardEventTimelineChartSerializer,
    CrmDashboardEventPerformanceSerializer,
)
from api.crm.utils.dashboard import DashboardUtils
from datetime import timedelta


class CrmDashboardEventViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Event Dashboard Analytics
    Provides various endpoints for event dashboard statistics and charts
    """

    model_class = EventSchedule
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated & IsStaffUser]
    filterset_class = CrmDashboardEventFilter
    swagger_tags = ["CRM Dashboard"]
    serializer_class = CrmDashboardEventSerializer

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = CacheManager("crm_dashboard_event")
        self.cache_timeout = 60 * 5  # 5 minutes

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for event schedules (non-deleted schedules only)
        """
        return EventSchedule.objects.filter(deleted=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "created_at_after": self.request.GET.get("created_at_after", ""),
            "created_at_before": self.request.GET.get("created_at_before", ""),
            "date_field": self.request.GET.get("date_field", ""),
            "programs": self.request.GET.get("programs", ""),
            "event": self.request.GET.get("event", ""),
            "stage": self.request.GET.get("stage", ""),
            "event_type": self.request.GET.get("event_type", ""),
            "modality": self.request.GET.get("modality", ""),
        }

    # ==== Utilities ====

    def _get_queryset_excluding_filters(self, exclude_fields):
        """
        Get queryset excluding specific filters for comparison calculations
        """
        return DashboardUtils.get_queryset_excluding_filters(
            self.get_queryset(), self.filterset_class, self.request, exclude_fields
        )

    def _get_report_dates(self):
        """
        Get current and previous period dates for trend calculations
        """
        return DashboardUtils.get_report_dates(self.request)

    # ==== CALCULATION FUNCTIONS ====

    def calculate_general_stats(self):
        """
        Calculate general event statistics with trends - OPTIMIZED
        """
        # Get filtered queryset
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules using efficient query
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Get report dates for trend analysis
        dates = self._get_report_dates()

        # Current period enrollments
        current_enrollments = enrollments.filter(
            created_at__gte=dates["current_start"], created_at__lte=dates["current_end"]
        )

        # Previous period enrollments for comparison
        previous_enrollments = enrollments.filter(
            created_at__gte=dates["previous_start"],
            created_at__lte=dates["previous_end"],
        )

        # Use aggregation for efficient calculation
        current_stats = current_enrollments.aggregate(
            total=Count("id"),
            with_contact=Count("id", filter=Q(has_contact=True)),
            without_contact=Count("id", filter=Q(has_contact=False)),
            already_leads=Count("id", filter=Q(already_lead=True)),
            need_conciliation=Count("id", filter=Q(needs_conciliation=True)),
            with_partnership=Count("id", filter=Q(partnership__isnull=False)),
        )

        previous_stats = previous_enrollments.aggregate(
            total=Count("id"),
            with_contact=Count("id", filter=Q(has_contact=True)),
            already_leads=Count("id", filter=Q(already_lead=True)),
        )

        # Calculate conversion metrics efficiently
        enrollment_emails = list(current_enrollments.values_list("email", flat=True))
        enrollment_to_sales = (
            Order.objects.filter(
                owner__email__in=enrollment_emails,
                stage=Order.SOLD_STAGE,
                deleted=False,
            ).count()
            if enrollment_emails
            else 0
        )

        prev_enrollment_emails = list(
            previous_enrollments.values_list("email", flat=True)
        )
        enrollment_to_sales_prev = (
            Order.objects.filter(
                owner__email__in=prev_enrollment_emails,
                stage=Order.SOLD_STAGE,
                deleted=False,
            ).count()
            if prev_enrollment_emails
            else 0
        )

        # Calculate trends using utility function
        total_change, total_tendency = DashboardUtils.calculate_percentage_change(
            current_stats["total"], previous_stats["total"]
        )
        contact_change, contact_tendency = DashboardUtils.calculate_percentage_change(
            current_stats["with_contact"], previous_stats["with_contact"]
        )
        leads_change, leads_tendency = DashboardUtils.calculate_percentage_change(
            current_stats["already_leads"], previous_stats["already_leads"]
        )
        sales_change, sales_tendency = DashboardUtils.calculate_percentage_change(
            enrollment_to_sales, enrollment_to_sales_prev
        )

        # Calculate percentages
        total = current_stats["total"]
        with_contact_percentage = (
            (current_stats["with_contact"] / total * 100) if total > 0 else 0
        )
        without_contact_percentage = (
            (current_stats["without_contact"] / total * 100) if total > 0 else 0
        )
        already_leads_percentage = (
            (current_stats["already_leads"] / total * 100) if total > 0 else 0
        )
        need_conciliation_percentage = (
            (current_stats["need_conciliation"] / total * 100) if total > 0 else 0
        )
        with_partnership_percentage = (
            (current_stats["with_partnership"] / total * 100) if total > 0 else 0
        )
        enrollment_to_sales_percentage = (
            (enrollment_to_sales / total * 100) if total > 0 else 0
        )

        return {
            "period": dates["current_start"].strftime("%B %Y"),
            "total_enrollments": {
                "current": current_stats["total"],
                "previous": previous_stats["total"],
                "percentage_change": round(total_change, 1),
                "tendency": total_tendency,
            },
            "with_contact": {
                "current": current_stats["with_contact"],
                "previous": previous_stats["with_contact"],
                "percentage": round(with_contact_percentage, 1),
                "percentage_change": round(contact_change, 1),
                "tendency": contact_tendency,
            },
            "without_contact": {
                "current": current_stats["without_contact"],
                "percentage": round(without_contact_percentage, 1),
            },
            "already_leads": {
                "current": current_stats["already_leads"],
                "previous": previous_stats["already_leads"],
                "percentage": round(already_leads_percentage, 1),
                "percentage_change": round(leads_change, 1),
                "tendency": leads_tendency,
            },
            "need_conciliation": {
                "current": current_stats["need_conciliation"],
                "percentage": round(need_conciliation_percentage, 1),
            },
            "with_partnership": {
                "current": current_stats["with_partnership"],
                "percentage": round(with_partnership_percentage, 1),
            },
            "enrollment_to_sales": {
                "current": enrollment_to_sales,
                "previous": enrollment_to_sales_prev,
                "percentage": round(enrollment_to_sales_percentage, 1),
                "percentage_change": round(sales_change, 1),
                "tendency": sales_tendency,
            },
        }

    def calculate_event_performance(self):
        """
        Calculate event performance metrics - OPTIMIZED
        """
        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Aggregate performance metrics by event using efficient queries
        performance_data = (
            event_schedules.values("event__eid", "event__name", "event__start_date")
            .annotate(
                total_schedules=Count("esid"),
                total_enrollments=Count(
                    "enrollment_records", filter=Q(enrollment_records__deleted=False)
                ),
                avg_enrollments_per_schedule=Avg(
                    Case(
                        When(enrollment_records__deleted=False, then=1),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
            )
            .order_by("-total_enrollments")
        )

        chart_data = []
        for item in performance_data:
            # Calculate performance score (weighted average)
            enrollments_score = min(item["total_enrollments"] / 10, 10)  # Max 10 points
            schedule_efficiency = item["avg_enrollments_per_schedule"] or 0
            performance_score = (enrollments_score * 0.6) + (schedule_efficiency * 0.4)

            # Count interests for this event
            interests_count = (
                EventScheduleEnrollment.objects.filter(
                    event_schedule__event__eid=item["event__eid"],
                    deleted=False,
                    interests__isnull=False,
                )
                .exclude(interests=[])
                .count()
            )

            chart_data.append(
                {
                    "event_name": item["event__name"],
                    "event_date": item["event__start_date"],
                    "total_schedules": item["total_schedules"],
                    "total_enrollments": item["total_enrollments"],
                    "avg_enrollments_per_schedule": round(
                        item["avg_enrollments_per_schedule"] or 0, 1
                    ),
                    "performance_score": round(performance_score, 1),
                    "interests_count": interests_count,
                }
            )

        return chart_data

    def calculate_timeline_chart(self):
        """
        Calculate enrollment timeline with diffusion channel breakdown - OPTIMIZED
        """
        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments with monthly aggregation
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Group by month and diffusion channel using TruncMonth
        monthly_data = (
            enrollments.annotate(month=TruncMonth("created_at"))
            .values("month", "diffusion_channel")
            .annotate(enrollments=Count("id"))
            .order_by("month", "diffusion_channel")
        )

        # Group by month and partnership
        monthly_partnership_data = (
            enrollments.annotate(month=TruncMonth("created_at"))
            .values("month", "partnership__name")
            .annotate(enrollments=Count("id"))
            .order_by("month", "partnership__name")
        )

        # Organize data for stacked chart
        timeline_data = {}

        # Process diffusion channel data
        for item in monthly_data:
            month_key = item["month"].strftime("%Y-%m")
            if month_key not in timeline_data:
                timeline_data[month_key] = {
                    "period": month_key,
                    "total_enrollments": 0,
                    "by_channel": {},
                    "by_partnership": {},
                }

            channel = item["diffusion_channel"] or "Sin especificar"
            timeline_data[month_key]["by_channel"][channel] = item["enrollments"]
            timeline_data[month_key]["total_enrollments"] += item["enrollments"]

        # Process partnership data
        for item in monthly_partnership_data:
            month_key = item["month"].strftime("%Y-%m")
            if month_key in timeline_data:
                partnership = item["partnership__name"] or "Sin partnership"
                timeline_data[month_key]["by_partnership"][partnership] = item[
                    "enrollments"
                ]

        return list(timeline_data.values())

    # ==== DASHBOARD ENDPOINTS ====

    @action(detail=False, methods=["GET"], url_path="summary")
    def summary(self, request):
        """
        Essential dashboard data - Fast loading
        Returns core metrics and basic visualizations
        """
        cache_key = (
            f"event_summary_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate essential data only
            stats = self.calculate_general_stats()

            # Build response data
            summary_data = {
                "stats": stats,
            }

            # Cache the result
            self.cache_manager.set(cache_key, summary_data, timeout=self.cache_timeout)

            return Response(summary_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating summary data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["GET"], url_path="analytics")
    def analytics(self, request):
        """
        Advanced analytics - Deferred loading
        Returns event performance and timeline analysis
        """
        cache_key = (
            f"event_analytics_{hash(str(sorted(self.get_cache_key_params().items())))}"
        )

        # Try to get from cache first
        cached_data = self.cache_manager.get(cache_key)
        if cached_data:
            return Response(cached_data)

        try:
            # Calculate analytics data
            event_performance = self.calculate_event_performance()
            timeline_chart = self.calculate_timeline_chart()

            # Build response data
            analytics_data = {
                "event_performance": event_performance,
                "timeline_chart": timeline_chart,
            }

            # Cache the result
            self.cache_manager.set(
                cache_key, analytics_data, timeout=self.cache_timeout
            )

            return Response(analytics_data)

        except Exception as e:
            return Response(
                {"error": f"Error calculating analytics data: {str(e)}"}, status=500
            )

    @action(detail=False, methods=["POST"])
    def invalidate_cache(self, request):
        """
        Invalidate all cached data for event dashboard
        """
        self.cache_manager.invalidate()
        return Response({"message": "Cache invalidated successfully"})
        cache_key = f"event_type_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Count by event type
        type_counts = (
            event_schedules.values("event__type")
            .annotate(count=Count("esid"))
            .order_by("-count")
        )

        total_count = sum(item["count"] for item in type_counts)

        chart_data = []
        for item in type_counts:
            event_type = item["event__type"]
            count = item["count"]
            percentage = (count / total_count * 100) if total_count > 0 else 0

            # Get display name for event type
            type_display = dict(Event.TYPE_CHOICES).get(event_type, event_type)

            chart_data.append(
                {
                    "type": event_type,
                    "type_display": type_display,
                    "count": count,
                    "percentage": round(percentage, 1),
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def diffusion_channel_chart(self, request):
        """
        Get diffusion channel distribution for pie chart
        """
        cache_key = f"diffusion_channel_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Count by diffusion channel
        channel_counts = (
            enrollments.values("diffusion_channel")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        total_count = sum(item["count"] for item in channel_counts)

        chart_data = []
        for item in channel_counts:
            channel = item["diffusion_channel"] or "Sin especificar"
            count = item["count"]
            percentage = (count / total_count * 100) if total_count > 0 else 0

            chart_data.append(
                {"channel": channel, "count": count, "percentage": round(percentage, 1)}
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def interests_chart(self, request):
        """
        Get interests distribution for bar chart (ordered by count)
        """
        cache_key = f"interests_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Count interests from JSON field using database aggregation
        # Note: This is a complex case where we need to process JSON data
        # We'll use a more efficient approach by processing in Python but minimizing queries
        interest_counts = {}
        enrollments_with_interests = enrollments.exclude(
            interests__isnull=True
        ).exclude(interests=[])

        for enrollment in enrollments_with_interests.only("interests"):
            if enrollment.interests:
                for interest in enrollment.interests:
                    if isinstance(interest, str):
                        interest_counts[interest] = interest_counts.get(interest, 0) + 1
                    elif isinstance(interest, dict) and "name" in interest:
                        interest_name = interest["name"]
                        interest_counts[interest_name] = (
                            interest_counts.get(interest_name, 0) + 1
                        )

        # Sort by count (descending)
        sorted_interests = sorted(
            interest_counts.items(), key=lambda x: x[1], reverse=True
        )

        total_count = sum(interest_counts.values())

        # Use serializer for consistent data structure
        chart_data = []
        for interest, count in sorted_interests:
            percentage = (count / total_count * 100) if total_count > 0 else 0

            data = {
                "interest": interest,
                "count": count,
                "percentage": round(percentage, 1),
            }

            # Validate with serializer
            serializer = CrmDashboardEventInterestsChartSerializer(data=data)
            if serializer.is_valid():
                chart_data.append(serializer.validated_data)

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def segmentation_chart(self, request):
        """
        Get contact segmentation chart (new vs existing users)
        """
        cache_key = f"segmentation_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments for these event schedules
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Count new vs existing users
        new_users = enrollments.filter(user__isnull=True).count()
        existing_users = enrollments.filter(user__isnull=False).count()

        total_count = new_users + existing_users

        chart_data = []

        if new_users > 0:
            chart_data.append(
                {
                    "segment": "new",
                    "count": new_users,
                    "percentage": (
                        round((new_users / total_count * 100), 1)
                        if total_count > 0
                        else 0
                    ),
                }
            )

        if existing_users > 0:
            chart_data.append(
                {
                    "segment": "existing",
                    "count": existing_users,
                    "percentage": (
                        round((existing_users / total_count * 100), 1)
                        if total_count > 0
                        else 0
                    ),
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def dynamic_metrics(self, request):
        """
        Get dynamic metrics for upcoming/current events
        """
        cache_key = f"dynamic_metrics_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        now = timezone.now()

        # Get upcoming events (next 30 days) and current events
        upcoming_events = EventSchedule.objects.filter(
            deleted=False, start_date__gte=now, start_date__lte=now + timedelta(days=30)
        ).order_by("start_date")

        current_events = EventSchedule.objects.filter(
            deleted=False, start_date__lte=now, end_date__gte=now
        ).order_by("start_date")

        # Combine and get unique events
        all_events = (upcoming_events | current_events).distinct()

        # Apply filters if any
        filterset = self.filterset_class(self.request.GET, queryset=all_events)
        if filterset.is_valid():
            all_events = filterset.qs

        metrics_data = []

        for event_schedule in all_events[:10]:  # Limit to 10 events
            # Calculate days until event
            days_until = (event_schedule.start_date.date() - now.date()).days
            is_current = event_schedule.start_date <= now <= event_schedule.end_date
            is_upcoming = event_schedule.start_date > now

            # Get enrollment count
            total_registered = EventScheduleEnrollment.objects.filter(
                event_schedule=event_schedule, deleted=False
            ).count()

            metrics_data.append(
                {
                    "event_name": event_schedule.event.name,
                    "event_schedule_name": event_schedule.name,
                    "start_date": event_schedule.start_date,
                    "end_date": event_schedule.end_date,
                    "stage": event_schedule.stage,
                    "total_registered": total_registered,
                    "days_until_event": days_until,
                    "is_current": is_current,
                    "is_upcoming": is_upcoming,
                }
            )

        # Cache the result
        self.cache_manager.set(cache_key, metrics_data, timeout=self.cache_timeout)

        return Response(metrics_data)

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """
        Get complete dashboard data in a single request
        """
        cache_key = f"event_dashboard_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get all dashboard data
        stats_response = self.stats(request)
        event_type_response = self.event_type_chart(request)
        diffusion_response = self.diffusion_channel_chart(request)
        interests_response = self.interests_chart(request)
        segmentation_response = self.segmentation_chart(request)
        dynamic_response = self.dynamic_metrics(request)
        timeline_response = self.timeline_chart(request)
        performance_response = self.event_performance(request)

        dashboard_data = {
            "stats": stats_response.data,
            "event_type_chart": event_type_response.data,
            "diffusion_channel_chart": diffusion_response.data,
            "interests_chart": interests_response.data,
            "segmentation_chart": segmentation_response.data,
            "dynamic_metrics": dynamic_response.data,
            "timeline_chart": timeline_response.data,
            "event_performance": performance_response.data,
        }

        # Cache the result
        self.cache_manager.set(cache_key, dashboard_data, timeout=self.cache_timeout)

        return Response(dashboard_data)

    @action(detail=False, methods=["get"])
    def timeline_chart(self, request):
        """
        Get enrollment timeline chart (monthly/weekly trends)
        """
        cache_key = f"timeline_chart_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Get enrollments with monthly aggregation
        enrollments = EventScheduleEnrollment.objects.filter(
            event_schedule__in=event_schedules, deleted=False
        )

        # Group by month using TruncMonth
        monthly_data = (
            enrollments.annotate(month=TruncMonth("created_at"))
            .values("month")
            .annotate(
                enrollments=Count("id"),
                events_count=Count("event_schedule__event", distinct=True),
            )
            .order_by("month")
        )

        chart_data = []
        for item in monthly_data:
            data = {
                "period": item["month"].strftime("%Y-%m"),
                "enrollments": item["enrollments"],
                "events_count": item["events_count"],
            }

            # Validate with serializer
            serializer = CrmDashboardEventTimelineChartSerializer(data=data)
            if serializer.is_valid():
                chart_data.append(serializer.validated_data)

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["get"])
    def event_performance(self, request):
        """
        Get event performance ranking
        """
        cache_key = f"event_performance_{hash(str(self.get_cache_key_params()))}"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return Response(cached_data)

        # Get filtered event schedules
        event_schedules = self.get_filtered_queryset()

        # Aggregate performance metrics by event using efficient queries
        performance_data = (
            event_schedules.values("event__name")
            .annotate(
                total_schedules=Count("esid"),
                total_enrollments=Count(
                    "enrollment_records", filter=Q(enrollment_records__deleted=False)
                ),
                avg_enrollments_per_schedule=Avg(
                    Case(
                        When(enrollment_records__deleted=False, then=1),
                        default=0,
                        output_field=IntegerField(),
                    )
                ),
            )
            .order_by("-total_enrollments")
        )

        chart_data = []
        for item in performance_data:
            # Calculate conversion to sales (simplified for performance)
            # In a real scenario, you might want to cache this or use a more efficient approach
            conversion_rate = 0.0  # Placeholder - would need complex query

            # Calculate performance score (weighted average)
            enrollments_score = min(item["total_enrollments"] / 10, 10)  # Max 10 points
            schedule_efficiency = item["avg_enrollments_per_schedule"] or 0
            performance_score = (enrollments_score * 0.6) + (schedule_efficiency * 0.4)

            data = {
                "event_name": item["event__name"],
                "total_schedules": item["total_schedules"],
                "total_enrollments": item["total_enrollments"],
                "avg_enrollments_per_schedule": round(
                    item["avg_enrollments_per_schedule"] or 0, 1
                ),
                "conversion_to_sales": conversion_rate,
                "performance_score": round(performance_score, 1),
            }

            # Validate with serializer
            serializer = CrmDashboardEventPerformanceSerializer(data=data)
            if serializer.is_valid():
                chart_data.append(serializer.validated_data)

        # Cache the result
        self.cache_manager.set(cache_key, chart_data, timeout=self.cache_timeout)

        return Response(chart_data)

    @action(detail=False, methods=["post"])
    def invalidate_cache(self, request):
        """
        Invalidate all cached data for event dashboard
        """
        self.cache_manager.invalidate()
        return Response({"message": "Cache invalidated successfully"})
