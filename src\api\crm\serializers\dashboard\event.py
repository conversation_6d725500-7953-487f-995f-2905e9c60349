"""
Event Dashboard Serializers for CRM
Provides serializers for event dashboard analytics data
"""

from rest_framework import serializers
from core.models import Event, Offering, EventSchedule


class CrmDashboardEventStatsSerializer(serializers.Serializer):
    """Serializer for event dashboard statistics"""

    # General stats
    total_enrollments = serializers.IntegerField(
        help_text="Total number of enrollments"
    )
    total_enrollments_change = serializers.FloatField(
        help_text="Percentage change from previous period"
    )
    total_enrollments_tendency = serializers.CharField(
        help_text="Trend direction: up, down, flat"
    )

    with_contact = serializers.IntegerField(
        help_text="Number of enrollments with contact"
    )
    with_contact_percentage = serializers.FloatField(
        help_text="Percentage of enrollments with contact"
    )
    with_contact_change = serializers.FloatField(
        help_text="Percentage change from previous period"
    )
    with_contact_tendency = serializers.CharField(
        help_text="Trend direction: up, down, flat"
    )

    without_contact = serializers.IntegerField(
        help_text="Number of enrollments without contact"
    )
    without_contact_percentage = serializers.FloatField(
        help_text="Percentage of enrollments without contact"
    )

    already_leads = serializers.IntegerField(
        help_text="Number of enrollments that are already leads"
    )
    already_leads_percentage = serializers.FloatField(
        help_text="Percentage of enrollments that are already leads"
    )
    already_leads_change = serializers.FloatField(
        help_text="Percentage change from previous period"
    )
    already_leads_tendency = serializers.CharField(
        help_text="Trend direction: up, down, flat"
    )

    need_conciliation = serializers.IntegerField(
        help_text="Number of enrollments that need conciliation"
    )
    need_conciliation_percentage = serializers.FloatField(
        help_text="Percentage of enrollments that need conciliation"
    )

    with_partnership = serializers.IntegerField(
        help_text="Number of enrollments with partnership"
    )
    with_partnership_percentage = serializers.FloatField(
        help_text="Percentage of enrollments with partnership"
    )

    # Conversion metrics
    enrollment_to_sales = serializers.IntegerField(
        help_text="Number of enrollments that resulted in sales"
    )
    enrollment_to_sales_percentage = serializers.FloatField(
        help_text="Conversion rate from enrollment to sales"
    )
    enrollment_to_sales_change = serializers.FloatField(
        help_text="Percentage change from previous period"
    )
    enrollment_to_sales_tendency = serializers.CharField(
        help_text="Trend direction: up, down, flat"
    )

    program_match_sales = serializers.IntegerField(
        help_text="Number of sales that match the event's program"
    )
    program_match_percentage = serializers.FloatField(
        help_text="Percentage of sales that match the event's program"
    )


class CrmDashboardEventTypeChartSerializer(serializers.Serializer):
    """Serializer for event type distribution chart"""

    type = serializers.CharField(help_text="Event type")
    type_display = serializers.CharField(help_text="Event type display name")
    count = serializers.IntegerField(help_text="Number of event schedules")
    percentage = serializers.FloatField(help_text="Percentage of total")


class CrmDashboardEventDiffusionChannelChartSerializer(serializers.Serializer):
    """Serializer for diffusion channel distribution chart"""

    channel = serializers.CharField(help_text="Diffusion channel")
    count = serializers.IntegerField(help_text="Number of enrollments")
    percentage = serializers.FloatField(help_text="Percentage of total")


class CrmDashboardEventInterestsChartSerializer(serializers.Serializer):
    """Serializer for interests distribution chart"""

    interest = serializers.CharField(help_text="Interest/specialization")
    count = serializers.IntegerField(
        help_text="Number of enrollments with this interest"
    )
    percentage = serializers.FloatField(help_text="Percentage of total")


class CrmDashboardEventSegmentationChartSerializer(serializers.Serializer):
    """Serializer for contact segmentation chart"""

    segment = serializers.CharField(help_text="Segment type: new or existing")
    count = serializers.IntegerField(help_text="Number of enrollments")
    percentage = serializers.FloatField(help_text="Percentage of total")


class CrmDashboardEventDynamicMetricsSerializer(serializers.Serializer):
    """Serializer for dynamic metrics of upcoming/current events"""

    event_name = serializers.CharField(help_text="Name of the event")
    event_schedule_name = serializers.CharField(help_text="Name of the event schedule")
    start_date = serializers.DateTimeField(help_text="Event start date")
    end_date = serializers.DateTimeField(help_text="Event end date")
    stage = serializers.CharField(help_text="Event stage")

    total_registered = serializers.IntegerField(
        help_text="Total number of registered participants"
    )
    email_invitations_sent = serializers.IntegerField(
        help_text="Number of email invitations sent"
    )
    whatsapp_invitations_sent = serializers.IntegerField(
        help_text="Number of WhatsApp invitations sent"
    )

    days_until_event = serializers.IntegerField(
        help_text="Days until event starts (negative if ongoing/past)"
    )
    is_current = serializers.BooleanField(
        help_text="Whether the event is currently happening"
    )
    is_upcoming = serializers.BooleanField(help_text="Whether the event is upcoming")


class CrmDashboardEventFilterOptionsSerializer(serializers.Serializer):
    """Serializer for filter options available in the dashboard"""

    programs = serializers.ListField(
        child=serializers.DictField(),
        help_text="Available programs/offerings for filtering",
    )
    events = serializers.ListField(
        child=serializers.DictField(), help_text="Available events for filtering"
    )
    stages = serializers.ListField(
        child=serializers.DictField(), help_text="Available event stages for filtering"
    )
    event_types = serializers.ListField(
        child=serializers.DictField(), help_text="Available event types for filtering"
    )
    modalities = serializers.ListField(
        child=serializers.DictField(),
        help_text="Available event modalities for filtering",
    )


# New BI-recommended serializers
class CrmDashboardEventTimelineChartSerializer(serializers.Serializer):
    """Serializer for enrollment timeline chart"""

    period = serializers.CharField(help_text="Time period (month/week)")
    enrollments = serializers.IntegerField(help_text="Number of enrollments in period")
    events_count = serializers.IntegerField(help_text="Number of events in period")


class CrmDashboardEventPerformanceSerializer(serializers.Serializer):
    """Serializer for event performance metrics"""

    event_name = serializers.CharField(help_text="Event name")
    total_schedules = serializers.IntegerField(
        help_text="Total schedules for this event"
    )
    total_enrollments = serializers.IntegerField(help_text="Total enrollments")
    avg_enrollments_per_schedule = serializers.FloatField(
        help_text="Average enrollments per schedule"
    )
    conversion_to_sales = serializers.FloatField(help_text="Conversion rate to sales")
    performance_score = serializers.FloatField(help_text="Overall performance score")


class CrmDashboardEventHeatmapSerializer(serializers.Serializer):
    """Serializer for enrollment heatmap by day/hour"""

    day_of_week = serializers.IntegerField(help_text="Day of week (0=Monday)")
    hour = serializers.IntegerField(help_text="Hour of day (0-23)")
    enrollments = serializers.IntegerField(help_text="Number of enrollments")


class CrmDashboardEventSerializer(serializers.Serializer):
    """Main serializer for event dashboard response"""

    stats = CrmDashboardEventStatsSerializer(help_text="General event statistics")
    event_type_chart = CrmDashboardEventTypeChartSerializer(
        many=True, help_text="Event type distribution for pie chart"
    )
    diffusion_channel_chart = CrmDashboardEventDiffusionChannelChartSerializer(
        many=True, help_text="Diffusion channel distribution for pie chart"
    )
    interests_chart = CrmDashboardEventInterestsChartSerializer(
        many=True, help_text="Interests distribution for bar chart"
    )
    segmentation_chart = CrmDashboardEventSegmentationChartSerializer(
        many=True, help_text="Contact segmentation for chart"
    )
    dynamic_metrics = CrmDashboardEventDynamicMetricsSerializer(
        many=True, help_text="Dynamic metrics for upcoming/current events"
    )
    # New BI-recommended charts
    timeline_chart = CrmDashboardEventTimelineChartSerializer(
        many=True, help_text="Enrollment timeline for line chart"
    )
    conversion_funnel = CrmDashboardEventConversionFunnelSerializer(
        many=True, help_text="Conversion funnel analysis"
    )
    event_performance = CrmDashboardEventPerformanceSerializer(
        many=True, help_text="Event performance ranking"
    )
    enrollment_heatmap = CrmDashboardEventHeatmapSerializer(
        many=True, help_text="Enrollment heatmap by day/hour"
    )
