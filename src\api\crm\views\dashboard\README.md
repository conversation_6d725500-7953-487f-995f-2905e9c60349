# Event Dashboard API Documentation

Este documento describe los endpoints del dashboard de eventos del CRM, proporcionando análisis y métricas sobre eventos y sus inscripciones.

## Base URL
```
/api/crm/dashboard/event/
```

## Autenticación
Todos los endpoints requieren:
- **Autenticación**: Token de autenticación
- **Permisos**: Usuario staff

## Filtros Disponibles

Todos los endpoints soportan los siguientes filtros como parámetros de consulta:

| Parámetro | Tipo | Descripción | Ejemplo |
|-----------|------|-------------|---------|
| `created_at_after` | Date | Fecha de inicio del rango (YYYY-MM-DD) | `2024-01-01` |
| `created_at_before` | Date | Fecha de fin del rango (YYYY-MM-DD) | `2024-12-31` |
| `date_field` | String | Campo de fecha a filtrar (`created_at`, `start_date`) | `start_date` |
| `programs` | String | IDs de programas separados por comas | `uuid1,uuid2` |
| `event` | UUID | ID específico de evento | `uuid` |
| `stage` | String | Etapas de evento separadas por comas | `planning,launched` |
| `event_type` | String | Tipos de evento separados por comas | `workshop,webinar` |
| `modality` | String | Modalidades separadas por comas | `remote,in_person` |
| `force_refresh` | Boolean | Forzar actualización de caché | `true` |

## Endpoints

### 1. Estadísticas Generales
**GET** `/stats/`

Obtiene estadísticas generales de eventos con análisis de tendencias.

#### Ejemplo de Respuesta:
```json
{
  "total_enrollments": 150,
  "total_enrollments_change": 25.5,
  "total_enrollments_tendency": "up",
  "with_contact": 90,
  "with_contact_percentage": 60.0,
  "with_contact_change": 15.2,
  "with_contact_tendency": "up",
  "without_contact": 60,
  "without_contact_percentage": 40.0,
  "already_leads": 45,
  "already_leads_percentage": 30.0,
  "already_leads_change": 8.7,
  "already_leads_tendency": "up",
  "need_conciliation": 12,
  "need_conciliation_percentage": 8.0,
  "with_partnership": 30,
  "with_partnership_percentage": 20.0,
  "enrollment_to_sales": 18,
  "enrollment_to_sales_percentage": 12.0,
  "enrollment_to_sales_change": 5.3,
  "enrollment_to_sales_tendency": "up",
  "program_match_sales": 15,
  "program_match_percentage": 83.3
}
```

#### Uso en Frontend:
- **Tarjetas de estadísticas**: Mostrar métricas principales con indicadores de tendencia
- **KPIs**: Conversión de inscripciones a ventas
- **Alertas**: Inscripciones que necesitan conciliación

### 2. Distribución por Tipo de Evento
**GET** `/event_type_chart/`

Obtiene la distribución de programaciones por tipo de evento.

#### Ejemplo de Respuesta:
```json
[
  {
    "type": "workshop",
    "type_display": "Workshop",
    "count": 25,
    "percentage": 45.5
  },
  {
    "type": "webinar",
    "type_display": "Webinar",
    "count": 20,
    "percentage": 36.4
  },
  {
    "type": "hands_of_workshop",
    "type_display": "Hands-on Workshop",
    "count": 10,
    "percentage": 18.1
  }
]
```

#### Uso en Frontend:
- **Gráfico de torta (Pie Chart)**: Visualizar distribución de tipos de eventos
- **Leyenda**: Mostrar `type_display` y porcentajes

### 3. Medios de Difusión
**GET** `/diffusion_channel_chart/`

Obtiene la distribución de inscripciones por canal de difusión.

#### Ejemplo de Respuesta:
```json
[
  {
    "channel": "Facebook",
    "count": 45,
    "percentage": 30.0
  },
  {
    "channel": "Instagram",
    "count": 38,
    "percentage": 25.3
  },
  {
    "channel": "Email",
    "count": 32,
    "percentage": 21.3
  },
  {
    "channel": "Sin especificar",
    "count": 35,
    "percentage": 23.4
  }
]
```

#### Uso en Frontend:
- **Gráfico de torta (Pie Chart)**: Visualizar efectividad de canales de difusión
- **Tabla**: Ranking de canales más efectivos

### 4. Intereses de Especialización
**GET** `/interests_chart/`

Obtiene el conteo de intereses de especialización ordenados de mayor a menor.

#### Ejemplo de Respuesta:
```json
[
  {
    "interest": "Inteligencia Artificial",
    "count": 65,
    "percentage": 28.5
  },
  {
    "interest": "Desarrollo Web",
    "count": 48,
    "percentage": 21.1
  },
  {
    "interest": "Data Science",
    "count": 42,
    "percentage": 18.4
  },
  {
    "interest": "Ciberseguridad",
    "count": 35,
    "percentage": 15.4
  }
]
```

#### Uso en Frontend:
- **Gráfico de barras horizontales**: Mostrar intereses más populares
- **Ordenamiento**: De mayor a menor demanda

### 5. Segmentación de Contactos
**GET** `/segmentation_chart/`

Obtiene la segmentación entre usuarios nuevos y existentes.

#### Ejemplo de Respuesta:
```json
[
  {
    "segment": "new",
    "count": 95,
    "percentage": 63.3
  },
  {
    "segment": "existing",
    "count": 55,
    "percentage": 36.7
  }
]
```

#### Uso en Frontend:
- **Gráfico de barras o torta**: Visualizar proporción de usuarios nuevos vs existentes
- **Métricas de adquisición**: Efectividad en captar nuevos usuarios

### 6. Métricas Dinámicas
**GET** `/dynamic_metrics/`

Obtiene métricas de eventos próximos o en curso.

#### Ejemplo de Respuesta:
```json
[
  {
    "event_name": "Workshop de IA",
    "event_schedule_name": "IA Básico - Enero 2024",
    "start_date": "2024-01-15T10:00:00Z",
    "end_date": "2024-01-15T16:00:00Z",
    "stage": "launched",
    "total_registered": 45,
    "email_invitations_sent": 0,
    "whatsapp_invitations_sent": 0,
    "days_until_event": 3,
    "is_current": false,
    "is_upcoming": true
  }
]
```

#### Uso en Frontend:
- **Tarjetas de eventos próximos**: Mostrar countdown y métricas en tiempo real
- **Dashboard en vivo**: Eventos actuales con inscripciones en tiempo real

### 7. Opciones de Filtros
**GET** `/filter_options/`

Obtiene las opciones disponibles para los filtros del dashboard.

#### Ejemplo de Respuesta:
```json
{
  "programs": [
    {
      "id": "uuid1",
      "name": "Especialización en IA",
      "code_name": "IA-2024-1"
    }
  ],
  "events": [
    {
      "id": "uuid2",
      "name": "Workshop de Machine Learning",
      "type": "workshop"
    }
  ],
  "stages": [
    {"value": "planning", "label": "Planning"},
    {"value": "launched", "label": "Launched"}
  ],
  "event_types": [
    {"value": "workshop", "label": "Workshop"},
    {"value": "webinar", "label": "Webinar"}
  ],
  "modalities": [
    {"value": "remote", "label": "Remote"},
    {"value": "in_person", "label": "In-Person"}
  ]
}
```

### 8. Dashboard Completo
**GET** `/dashboard/`

Obtiene todos los datos del dashboard en una sola petición.

#### Ejemplo de Respuesta:
```json
{
  "stats": { /* datos de /stats/ */ },
  "event_type_chart": [ /* datos de /event_type_chart/ */ ],
  "diffusion_channel_chart": [ /* datos de /diffusion_channel_chart/ */ ],
  "interests_chart": [ /* datos de /interests_chart/ */ ],
  "segmentation_chart": [ /* datos de /segmentation_chart/ */ ],
  "dynamic_metrics": [ /* datos de /dynamic_metrics/ */ ]
}
```

### 9. Timeline de Inscripciones (NUEVO - BI)
**GET** `/timeline_chart/`

Obtiene la evolución temporal de inscripciones por mes.

#### Ejemplo de Respuesta:
```json
[
  {
    "period": "2024-01",
    "enrollments": 45,
    "events_count": 8
  },
  {
    "period": "2024-02",
    "enrollments": 62,
    "events_count": 12
  }
]
```

#### Uso en Frontend:
- **Gráfico de líneas**: Mostrar tendencia de inscripciones en el tiempo
- **Gráfico de barras combinado**: Inscripciones vs número de eventos por mes

### 10. Embudo de Conversión (NUEVO - BI)
**GET** `/conversion_funnel/`

Obtiene el análisis de embudo de conversión desde inscripción hasta venta.

#### Ejemplo de Respuesta:
```json
[
  {
    "stage": "Inscripciones",
    "count": 150,
    "percentage": 100.0,
    "conversion_rate": 100.0
  },
  {
    "stage": "Con Contacto",
    "count": 90,
    "percentage": 60.0,
    "conversion_rate": 60.0
  },
  {
    "stage": "Ya son Leads",
    "count": 45,
    "percentage": 30.0,
    "conversion_rate": 50.0
  },
  {
    "stage": "Con Partnership",
    "count": 30,
    "percentage": 20.0,
    "conversion_rate": 66.7
  },
  {
    "stage": "Ventas",
    "count": 18,
    "percentage": 12.0,
    "conversion_rate": 60.0
  }
]
```

#### Uso en Frontend:
- **Gráfico de embudo**: Visualizar pérdidas en cada etapa del proceso
- **Métricas de conversión**: Identificar cuellos de botella

### 11. Rendimiento de Eventos (NUEVO - BI)
**GET** `/event_performance/`

Obtiene el ranking de rendimiento de eventos basado en múltiples métricas.

#### Ejemplo de Respuesta:
```json
[
  {
    "event_name": "Workshop de IA",
    "total_schedules": 5,
    "total_enrollments": 125,
    "avg_enrollments_per_schedule": 25.0,
    "conversion_to_sales": 15.2,
    "performance_score": 8.7
  },
  {
    "event_name": "Webinar de Data Science",
    "total_schedules": 3,
    "total_enrollments": 89,
    "avg_enrollments_per_schedule": 29.7,
    "conversion_to_sales": 12.4,
    "performance_score": 7.9
  }
]
```

#### Uso en Frontend:
- **Tabla de ranking**: Ordenar eventos por performance_score
- **Gráfico de barras**: Comparar métricas entre eventos
- **Scatter plot**: Inscripciones vs conversión a ventas

### 12. Invalidar Caché
**POST** `/invalidate_cache/`

Invalida toda la caché del dashboard de eventos.

#### Ejemplo de Respuesta:
```json
{
  "message": "Cache invalidated successfully"
}
```

## Ejemplos de Uso

### Filtrar por Rango de Fechas de Programación
```
GET /api/crm/dashboard/event/stats/?created_at_after=2024-01-01&created_at_before=2024-01-31&date_field=start_date
```

### Filtrar por Programas Específicos
```
GET /api/crm/dashboard/event/dashboard/?programs=uuid1,uuid2,uuid3
```

### Filtrar por Evento y Tipo
```
GET /api/crm/dashboard/event/interests_chart/?event=uuid&event_type=workshop,webinar
```

## Caché

- **Tiempo de caché**: 5 minutos para datos dinámicos, 30 minutos para opciones de filtros
- **Invalidación**: Usar `force_refresh=true` o endpoint `/invalidate_cache/`
- **Claves de caché**: Basadas en parámetros de filtros para optimizar rendimiento

## Análisis BI - Métricas Recomendadas

### Métricas Clave de Rendimiento (KPIs)

1. **Tasa de Conversión Global**: Inscripciones → Ventas
2. **Eficiencia de Eventos**: Inscripciones promedio por programación
3. **Calidad de Leads**: Porcentaje de inscripciones que ya son leads
4. **Efectividad de Canales**: ROI por canal de difusión
5. **Tendencia de Crecimiento**: Variación mensual de inscripciones

### Gráficos Recomendados por Tipo

#### Para Ejecutivos (Vista Estratégica)
- **KPI Cards**: Stats principales con tendencias
- **Funnel Chart**: Embudo de conversión
- **Line Chart**: Timeline de inscripciones
- **Performance Ranking**: Top eventos por score

#### Para Gerentes de Marketing (Vista Táctica)
- **Pie Charts**: Canales de difusión y tipos de evento
- **Bar Chart**: Intereses más demandados
- **Segmentation Chart**: Nuevos vs existentes
- **Heatmap**: Patrones de inscripción por día/hora

#### Para Coordinadores de Eventos (Vista Operativa)
- **Dynamic Metrics**: Eventos próximos en tiempo real
- **Event Performance**: Ranking detallado de eventos
- **Timeline**: Tendencias mensuales para planificación

### Insights Accionables

1. **Optimización de Canales**: Identificar canales con mejor conversión
2. **Planificación de Eventos**: Tipos de evento más exitosos
3. **Segmentación de Audiencia**: Adaptar estrategias según perfil
4. **Timing Óptimo**: Mejores momentos para lanzar eventos
5. **Gestión de Pipeline**: Identificar cuellos de botella en conversión

### Alertas Automáticas Sugeridas

- Eventos con baja inscripción (< 50% del promedio)
- Canales con conversión decreciente
- Inscripciones que necesitan seguimiento urgente
- Eventos próximos sin suficientes registros

## Notas de Implementación

1. **Tendencias**: Se calculan comparando el período actual con el período anterior de igual duración
2. **Filtros de fecha**: `date_field` determina si filtrar por fecha de creación o programación
3. **Conversiones**: Se rastrean inscripciones que resultaron en ventas mediante email matching
4. **Intereses**: Se extraen del campo JSON `interests` en las inscripciones
5. **Métricas dinámicas**: Se limitan a eventos en los próximos 30 días o actualmente en curso
6. **Agregaciones optimizadas**: Se usan agregaciones de Django para mejor rendimiento
7. **Validación con serializers**: Todos los datos se validan para consistencia y documentación
