# Event Dashboard API Documentation

Este documento describe los endpoints del dashboard de eventos del CRM, proporcionando análisis y métricas sobre eventos y sus inscripciones.

## Base URL
```
/api/crm/dashboard/event/
```

## Autenticación
Todos los endpoints requieren:
- **Autenticación**: Token de autenticación
- **Permisos**: Usuario staff

## Filtros Disponibles

Todos los endpoints soportan los siguientes filtros como parámetros de consulta:

| Parámetro | Tipo | Descripción | Ejemplo |
|-----------|------|-------------|---------|
| `created_at_after` | Date | Fecha de inicio del rango (YYYY-MM-DD) | `2024-01-01` |
| `created_at_before` | Date | Fecha de fin del rango (YYYY-MM-DD) | `2024-12-31` |
| `date_field` | String | Campo de fecha a filtrar (`created_at`, `start_date`) | `start_date` |
| `programs` | String | IDs de programas separados por comas | `uuid1,uuid2` |
| `event` | UUID | ID específico de evento | `uuid` |
| `stage` | String | Etapas de evento separadas por comas | `planning,launched` |
| `event_type` | String | Tipos de evento separados por comas | `workshop,webinar` |
| `modality` | String | Modalidades separadas por comas | `remote,in_person` |
| `force_refresh` | Boolean | Forzar actualización de caché | `true` |

## Endpoints Organizados

### 1. Summary - Datos Esenciales (Carga Rápida)
**GET** `/summary/`

Obtiene las métricas principales del dashboard con estructura organizada similar al sales dashboard.

#### Ejemplo de Respuesta:
```json
{
  "stats": {
    "period": "January 2024",
    "total_enrollments": {
      "current": 150,
      "previous": 120,
      "percentage_change": 25.0,
      "tendency": "up"
    },
    "with_contact": {
      "current": 90,
      "previous": 75,
      "percentage": 60.0,
      "percentage_change": 20.0,
      "tendency": "up"
    },
    "without_contact": {
      "current": 60,
      "percentage": 40.0
    },
    "already_leads": {
      "current": 45,
      "previous": 35,
      "percentage": 30.0,
      "percentage_change": 28.6,
      "tendency": "up"
    },
    "need_conciliation": {
      "current": 12,
      "percentage": 8.0
    },
    "with_partnership": {
      "current": 30,
      "percentage": 20.0
    },
    "enrollment_to_sales": {
      "current": 18,
      "previous": 14,
      "percentage": 12.0,
      "percentage_change": 28.6,
      "tendency": "up"
    }
  }
}
```

#### Uso en Frontend:
- **Tarjetas KPI**: Mostrar métricas principales con indicadores de tendencia
- **Carga inicial**: Datos esenciales para mostrar inmediatamente
- **Comparación temporal**: Tendencias vs período anterior

### 2. Analytics - Análisis Avanzado (Carga Diferida)
**GET** `/analytics/`

Obtiene análisis avanzados incluyendo rendimiento de eventos y timeline histórico.

#### Ejemplo de Respuesta:
```json
{
  "event_performance": [
    {
      "event_name": "Workshop de IA",
      "event_date": "2024-01-15T10:00:00Z",
      "total_schedules": 5,
      "total_enrollments": 125,
      "avg_enrollments_per_schedule": 25.0,
      "performance_score": 8.7,
      "interests_count": 89
    },
    {
      "event_name": "Webinar de Data Science",
      "event_date": "2024-01-20T14:00:00Z",
      "total_schedules": 3,
      "total_enrollments": 89,
      "avg_enrollments_per_schedule": 29.7,
      "performance_score": 7.9,
      "interests_count": 67
    }
  ],
  "timeline_chart": [
    {
      "period": "2024-01",
      "total_enrollments": 45,
      "by_channel": {
        "Facebook": 20,
        "Instagram": 15,
        "Email": 10
      },
      "by_partnership": {
        "Universidad ABC": 25,
        "Sin partnership": 20
      }
    }
  ]
}
```

#### Uso en Frontend:

**Event Performance Table:**
- **Tabla de ranking**: Ordenar eventos por performance_score
- **Columnas**: Nombre, fecha, programaciones, inscripciones, promedio, score, intereses
- **Sorting**: Por cualquier columna, especialmente por performance_score

**Timeline Chart:**
- **Gráfico de líneas**: Evolución temporal de inscripciones
- **Stacked Bar Chart**: Inscripciones por canal de difusión por mes
- **Stacked Area Chart**: Inscripciones por partnership por mes
- **Filtros interactivos**: Mostrar/ocultar canales o partnerships específicos

### 3. Invalidar Caché
**POST** `/invalidate_cache/`

Invalida toda la caché del dashboard de eventos.

#### Ejemplo de Respuesta:
```json
{
  "message": "Cache invalidated successfully"
}
```

## Estructura de Datos Mejorada

### Métricas con Tendencias
Todas las métricas principales incluyen:
- `current`: Valor actual
- `previous`: Valor del período anterior (cuando aplica)
- `percentage`: Porcentaje del total (cuando aplica)
- `percentage_change`: Cambio porcentual vs período anterior
- `tendency`: Dirección de la tendencia ("up", "down", "flat")

### Performance Score
Algoritmo ponderado que considera:
- **Inscripciones totales (60%)**: Normalizado a escala de 10
- **Eficiencia por programación (40%)**: Promedio de inscripciones por programación

### Timeline con Segmentación
Datos históricos organizados por:
- **Período**: Mes en formato YYYY-MM
- **Total**: Inscripciones totales del mes
- **Por canal**: Desglose por diffusion_channel
- **Por partnership**: Desglose por partnership

## Optimizaciones Implementadas

### 1. Agregaciones de Django
- Uso de `Count()` con filtros condicionales
- `TruncMonth()` para agrupación temporal eficiente
- Queries optimizadas para evitar N+1 problems

### 2. Caché Inteligente
- **Claves específicas**: Basadas en parámetros de filtros
- **Timeouts diferenciados**: 5 minutos para datos dinámicos
- **Invalidación selectiva**: Por endpoint o global

### 3. Estructura Modular
- **Funciones de cálculo separadas**: Reutilizables y testeable
- **Endpoints organizados**: Summary vs Analytics
- **Manejo de errores**: Try/catch con mensajes descriptivos

## Ejemplos de Uso

### Filtrar por Rango de Fechas de Programación
```
GET /api/crm/dashboard/event/summary/?created_at_after=2024-01-01&created_at_before=2024-01-31&date_field=start_date
```

### Filtrar por Programas Específicos
```
GET /api/crm/dashboard/event/analytics/?programs=uuid1,uuid2,uuid3
```

### Obtener Datos Completos con Caché Forzado
```
GET /api/crm/dashboard/event/summary/?force_refresh=true
GET /api/crm/dashboard/event/analytics/?force_refresh=true
```

## Recomendaciones de Implementación Frontend

### Carga Progresiva
1. **Inicial**: Cargar `/summary/` para mostrar KPIs inmediatamente
2. **Diferida**: Cargar `/analytics/` en segundo plano o bajo demanda
3. **Actualización**: Usar `force_refresh=true` solo cuando sea necesario

### Visualizaciones Recomendadas
- **KPI Cards**: Para stats del summary
- **Performance Table**: Para event_performance con sorting
- **Line Chart**: Para timeline_chart (total_enrollments)
- **Stacked Charts**: Para desglose por canal/partnership
- **Trend Indicators**: Flechas/colores basados en tendency

### Manejo de Estados
- **Loading**: Mostrar skeletons durante carga
- **Error**: Mostrar mensajes de error amigables
- **Empty**: Manejar casos sin datos
- **Refresh**: Botón para invalidar caché cuando sea necesario
